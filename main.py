#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
星图超级接口线上化 - Main FastAPI Application
"""

import time
import uuid
from typing import Dict, List, Optional, Any
from pathlib import Path

from fastapi import FastAPI, HTTPException, BackgroundTasks, Query, Depends, Request
from fastapi.responses import JSONResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from pydantic import BaseModel
import structlog

# Load environment variables first
from dotenv import load_dotenv
load_dotenv()

from config.settings import settings, validate_settings
from config.api_endpoints import get_all_categories, ENDPOINT_CATEGORIES
from core.cookie_manager import CookieManager
from core.xingtu_client import XingtuClient
from core.excel_exporter import ExcelExporter
from utils.logger import setup_logger, RequestLogger, log_health_check
from utils.validators import validate_author_id
from utils.security import (
    rate_limiter, api_key_manager, sensitive_filter,
    SecurityHeaderMiddleware, hash_ip
)


# Initialize logging
logger = setup_logger("xingtu_api", settings.app.log_level)
request_logger = RequestLogger(logger)

# Validate settings on startup (skip for testing)
try:
    validate_settings()
    logger.info("Settings validation passed")
except ValueError as e:
    logger.error("Settings validation failed", error=str(e))
    # Don't raise in development mode to allow testing
    if settings.app.env == "production":
        raise


# Initialize FastAPI app
app = FastAPI(
    title="星图超级接口线上化",
    description="Production-ready API for fetching comprehensive Xingtu influencer data with dynamic cookie management",
    version="1.0.0",
    docs_url="/docs" if settings.app.debug else None,
    redoc_url="/redoc" if settings.app.debug else None
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.app.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

if settings.app.env == "production":
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"]  # Configure based on your deployment
    )

# Initialize components
cookie_manager = CookieManager()
xingtu_client = XingtuClient(cookie_manager)
excel_exporter = ExcelExporter()


# Request/Response models
class AuthorInfoRequest(BaseModel):
    author_id: str
    endpoints: Optional[List[str]] = None
    category: Optional[str] = None


class ExportRequest(BaseModel):
    author_id: str
    format: str = "excel"  # excel, csv, json
    include_raw_data: bool = True
    include_summary: bool = True
    filename: Optional[str] = None


class BatchRequest(BaseModel):
    author_ids: List[str]
    format: str = "excel"
    max_concurrent: int = 5


# Dependency for enhanced API key authentication
async def verify_api_key(request: Request, api_key: Optional[str] = Query(None)):
    client_ip = request.client.host
    
    # 检查速率限制
    allowed, message = rate_limiter.is_allowed(client_ip)
    if not allowed:
        logger.warning("Rate limit exceeded", ip=hash_ip(client_ip), message=message)
        raise HTTPException(status_code=429, detail=message)
    
    # 验证API密钥
    if settings.app.api_key:  # 如果配置了API密钥，则必须验证
        valid, message = api_key_manager.validate_and_track(api_key, client_ip)
        if not valid:
            raise HTTPException(status_code=401, detail=message)
    
    return api_key


# Enhanced security and logging middleware
@app.middleware("http")
async def security_and_logging_middleware(request, call_next):
    request_id = str(uuid.uuid4())
    start_time = time.time()
    client_ip = request.client.host
    
    # 记录请求（使用哈希IP保护隐私）
    if settings.app.enable_request_logging:
        request_logger.log_request(
            request_id=request_id,
            method=request.method,
            path=request.url.path,
            client_ip=hash_ip(client_ip),
            user_agent=request.headers.get("user-agent")
        )
    
    # 处理请求
    response = await call_next(request)
    
    # 添加安全响应头
    response = SecurityHeaderMiddleware.add_security_headers(response)
    
    # 记录响应
    if settings.app.enable_request_logging:
        response_time = time.time() - start_time
        request_logger.log_response(
            request_id=request_id,
            status_code=response.status_code,
            response_time=response_time
        )
    
    return response


# Health check endpoint
@app.get("/health", summary="Health Check")
async def health_check():
    """Check the health status of all components"""
    try:
        # Check cookie manager
        cookie_health = cookie_manager.is_healthy()
        
        # Check Xingtu client
        client_status = xingtu_client.get_health_status()
        
        # Check export directory
        export_info = excel_exporter.get_export_info()
        
        health_status = {
            "status": "healthy" if cookie_health else "unhealthy",
            "timestamp": time.time(),
            "components": {
                "cookie_manager": {
                    "healthy": cookie_health,
                    "details": sensitive_filter.filter_cookie_info(cookie_manager.get_cookies_info())
                },
                "xingtu_client": {
                    "healthy": client_status["session_ready"] and client_status["cookies_available"],
                    "details": {
                        "session_ready": client_status["session_ready"],
                        "cookies_available": client_status["cookies_available"]
                    }
                },
                "excel_exporter": {
                    "healthy": "error" not in export_info,
                    "details": {
                        "export_dir_exists": export_info.get("export_dir_exists"),
                        "writable": export_info.get("writable")
                    }
                }
            }
        }
        
        # 过滤敏感信息
        health_status = sensitive_filter.filter_health_check_data(health_status)
        
        log_health_check(
            logger, 
            "overall", 
            health_status["status"], 
            health_status["components"]
        )
        
        status_code = 200 if cookie_health else 503
        return JSONResponse(content=health_status, status_code=status_code)
        
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        return JSONResponse(
            content={"status": "error", "error": str(e)}, 
            status_code=500
        )


# Main author info endpoint
@app.get("/author/{author_id}", summary="Get Comprehensive Author Info")
async def get_author_info(
    request: Request,
    author_id: str,
    endpoints: Optional[str] = Query(None, description="Comma-separated list of specific endpoints"),
    category: Optional[str] = Query(None, description="Endpoint category filter"),
    api_key: Optional[str] = Depends(verify_api_key)
):
    """
    Retrieve comprehensive information for a Xingtu author by their ID.
    
    - **author_id**: The unique identifier for the Xingtu author
    - **endpoints**: Optional comma-separated list of specific endpoints to fetch
    - **category**: Optional category filter (basic, commerce, analytics, etc.)
    """
    try:
        if not validate_author_id(author_id):
            raise HTTPException(status_code=400, detail="Invalid author ID format")
        
        # Parse endpoints parameter
        endpoint_list = None
        if endpoints:
            endpoint_list = [ep.strip() for ep in endpoints.split(",")]
        
        # Fetch author data
        if category:
            author_data = await xingtu_client.get_author_info_by_category(author_id, category)
        else:
            author_data = await xingtu_client.get_author_info(author_id, endpoint_list)
        
        # Check for errors
        if "error" in author_data and not author_data.get("_metadata"):
            raise HTTPException(status_code=500, detail=author_data["error"])
        
        return JSONResponse(content=author_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error fetching author info", author_id=author_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


# Export endpoint
@app.post("/export", summary="Export Author Data")
async def export_author_data(
    http_request: Request,
    request: ExportRequest,
    background_tasks: BackgroundTasks,
    api_key: Optional[str] = Depends(verify_api_key)
):
    """
    Export author data to various formats (Excel, CSV, JSON).
    """
    try:
        if not validate_author_id(request.author_id):
            raise HTTPException(status_code=400, detail="Invalid author ID format")
        
        # Fetch author data
        author_data = await xingtu_client.get_author_info(request.author_id)
        
        if "error" in author_data and not author_data.get("_metadata"):
            raise HTTPException(status_code=500, detail=author_data["error"])
        
        # Export based on format
        if request.format.lower() == "excel":
            file_path = excel_exporter.export_to_excel(
                author_data,
                request.filename,
                request.include_raw_data,
                request.include_summary
            )
        elif request.format.lower() == "csv":
            file_path = excel_exporter.export_to_csv(author_data, request.filename)
        elif request.format.lower() == "json":
            # For JSON, return the data directly
            return JSONResponse(content=author_data)
        else:
            raise HTTPException(status_code=400, detail="Unsupported export format")
        
        if not file_path:
            raise HTTPException(status_code=500, detail="Export failed")
        
        # Return file download
        return FileResponse(
            path=file_path,
            filename=file_path.name,
            media_type="application/octet-stream"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error exporting author data", author_id=request.author_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Export error: {str(e)}")


# Batch processing endpoint
@app.post("/batch", summary="Batch Process Multiple Authors")
async def batch_process_authors(
    http_request: Request,
    request: BatchRequest,
    api_key: Optional[str] = Depends(verify_api_key)
):
    """
    Process multiple authors in batch and export to single file.
    """
    try:
        if len(request.author_ids) > 100:  # Limit batch size
            raise HTTPException(status_code=400, detail="Batch size too large (max 100)")
        
        # Validate all author IDs
        invalid_ids = [aid for aid in request.author_ids if not validate_author_id(aid)]
        if invalid_ids:
            raise HTTPException(status_code=400, detail=f"Invalid author IDs: {invalid_ids}")
        
        # Process authors
        authors_data = []
        for author_id in request.author_ids:
            try:
                author_data = await xingtu_client.get_author_info(author_id)
                authors_data.append(author_data)
            except Exception as e:
                logger.warning("Failed to fetch author data", author_id=author_id, error=str(e))
                # Add error record
                authors_data.append({
                    "_metadata": {"author_id": author_id},
                    "error": str(e)
                })
        
        # Export batch data
        if request.format.lower() == "excel":
            file_path = excel_exporter.export_multiple_authors(authors_data)
        elif request.format.lower() == "json":
            return JSONResponse(content={"authors": authors_data})
        else:
            raise HTTPException(status_code=400, detail="Unsupported batch export format")
        
        if not file_path:
            raise HTTPException(status_code=500, detail="Batch export failed")
        
        return FileResponse(
            path=file_path,
            filename=file_path.name,
            media_type="application/octet-stream"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error in batch processing", error=str(e))
        raise HTTPException(status_code=500, detail=f"Batch processing error: {str(e)}")


# Endpoints information
@app.get("/endpoints", summary="Get Available API Endpoints")
async def get_endpoints():
    """Get information about available Xingtu API endpoints"""
    endpoints = xingtu_client.get_available_endpoints()
    categories = get_all_categories()
    
    return {
        "total_endpoints": len(endpoints),
        "categories": {cat: ENDPOINT_CATEGORIES.get(cat, "") for cat in categories},
        "endpoints": endpoints
    }


# Cookie management endpoints
@app.post("/cookies/refresh", summary="Refresh Cookies")
async def refresh_cookies(request: Request, api_key: Optional[str] = Depends(verify_api_key)):
    """Force refresh cookies from CookieCloud"""
    try:
        success = cookie_manager.refresh_cookies()
        if success:
            return {"status": "success", "message": "Cookies refreshed successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to refresh cookies")
    except Exception as e:
        logger.error("Error refreshing cookies", error=str(e))
        raise HTTPException(status_code=500, detail=f"Cookie refresh error: {str(e)}")


@app.get("/cookies/info", summary="Get Cookie Information")
async def get_cookie_info(request: Request, api_key: Optional[str] = Depends(verify_api_key)):
    """Get filtered information about stored cookies (sensitive data removed)"""
    try:
        cookie_info = cookie_manager.get_cookies_info()
        # 过滤敏感信息
        filtered_info = sensitive_filter.filter_cookie_info(cookie_info)
        return JSONResponse(content=filtered_info)
    except Exception as e:
        logger.error("Error getting cookie info", error=str(e))
        raise HTTPException(status_code=500, detail=f"Cookie info error: {str(e)}")


# Root endpoint
@app.get("/", summary="API Information")
async def root():
    """Get basic API information"""
    return {
        "name": "星图超级接口线上化",
        "version": "1.0.0",
        "description": "Production-ready API for fetching comprehensive Xingtu influencer data",
        "endpoints": {
            "health": "/health",
            "author_info": "/author/{author_id}",
            "export": "/export",
            "batch": "/batch",
            "endpoints": "/endpoints",
            "docs": "/docs" if settings.app.debug else "disabled"
        },
        "status": "online"
    }


if __name__ == "__main__":
    import uvicorn
    
    logger.info("Starting 星图超级接口线上化 server...")
    uvicorn.run(
        "main:app",
        host=settings.app.host,
        port=settings.app.port,
        reload=settings.app.debug,
        log_level=settings.app.log_level.lower()
    )
