#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：获取指定达人信息
"""

import asyncio
import json
from pathlib import Path

# 添加项目路径到sys.path
import sys
sys.path.append(str(Path(__file__).parent))

from core.cookie_manager import CookieManager
from core.xingtu_client import XingtuClient
from config.settings import settings
from utils.logger import setup_logger


async def test_author_info(author_id: str):
    """测试获取达人信息"""
    
    print(f"开始测试获取达人ID: {author_id} 的信息...")
    
    # 初始化日志
    logger = setup_logger("test_author", "INFO")
    
    try:
        # 初始化组件
        print("1. 初始化CookieManager...")
        cookie_manager = CookieManager()
        
        print("2. 检查cookie健康状态...")
        if not cookie_manager.is_healthy():
            print("⚠️  Cookie Manager不健康，正在尝试刷新cookies...")
            success = await cookie_manager.refresh_cookies()
            if not success:
                print("❌ 无法获取有效的cookies，请检查CookieCloud配置")
                return
        
        print("3. 初始化XingtuClient...")
        xingtu_client = XingtuClient(cookie_manager)
        
        print("4. 检查客户端健康状态...")
        health_status = xingtu_client.get_health_status()
        print(f"客户端状态: {json.dumps(health_status, ensure_ascii=False, indent=2)}")
        
        print("5. 获取达人信息...")
        result = await xingtu_client.get_author_info(author_id)
        
        print("\n" + "="*50)
        print("测试结果:")
        print("="*50)
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        # 保存结果到文件
        output_file = f"test_result_{author_id}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"\n结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 测试达人ID
    test_author_id = "7119367979820646432"
    
    print(f"星图超级接口测试")
    print(f"目标达人ID: {test_author_id}")
    print("-" * 50)
    
    # 运行异步测试
    asyncio.run(test_author_info(test_author_id)) 