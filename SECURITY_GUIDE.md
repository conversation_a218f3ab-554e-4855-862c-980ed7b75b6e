# 🔐 星图超级接口安全配置指南

## 📋 安全策略总览

你的API现在具备了多层安全防护机制，包括：

### ✅ 已启用的安全措施

1. **强制API密钥认证**
   - 所有API调用都需要有效的API密钥
   - API密钥: `XingTu2025SuperSecureKey_dxy_production_v1.0`
   - 每日使用限制: 1000次/密钥

2. **智能速率限制**
   - 每分钟最多30次请求
   - 每小时最多100次请求
   - 超限自动封IP（5分钟-1小时）

3. **敏感数据保护**
   - Cookie信息已过滤，不暴露敏感内容
   - 健康检查接口移除敏感信息
   - IP地址哈希化存储

4. **增强安全响应头**
   - XSS保护
   - 内容类型保护
   - 点击劫持保护
   - HTTPS强制
   - CSP策略

5. **CORS限制**
   - 仅允许指定域名访问
   - 当前配置需要你手动设置允许的域名

6. **生产环境配置**
   - 调试模式已关闭
   - API文档已隐藏
   - 安全日志记录

## 🔑 API密钥使用方法

所有API请求都必须包含API密钥：

```bash
# 方法1: 查询参数
curl "https://your-api-domain.com/author/7119367979820646432?api_key=XingTu2025SuperSecureKey_dxy_production_v1.0"

# 方法2: JavaScript
fetch('https://your-api-domain.com/author/7119367979820646432?api_key=XingTu2025SuperSecureKey_dxy_production_v1.0')
```

## ⚙️ 需要手动配置的设置

### 1. 更新CORS域名
编辑 `.env` 文件，将以下内容替换为你的实际域名：

```env
CORS_ORIGINS=https://your-actual-domain.com,https://your-admin-panel.com
```

### 2. 可选：更换API密钥
如果需要更安全的密钥，运行以下Python代码：

```python
import secrets
new_key = f"xingtu_{secrets.token_urlsafe(32)}"
print(f"新的API密钥: {new_key}")
```

然后在 `.env` 文件中更新：
```env
API_KEY=xingtu_你的新密钥
```

## 🚫 访问限制

### 速率限制
- **分钟级**: 30次/分钟
- **小时级**: 100次/小时
- **超限处理**: 自动封IP

### IP封锁机制
- 超过分钟限制 → 封锁5分钟
- 超过小时限制 → 封锁1小时
- 多次违规 → 自动延长封锁时间

### API使用配额
- 每个API密钥每日限制: 1000次
- 凌晨自动重置计数

## 🛡️ 敏感信息保护

### Cookie安全
- ✅ CookieCloud连接使用HTTPS
- ✅ Cookie内容不在API响应中暴露
- ✅ 仅返回统计信息（数量、状态）

### 日志安全
- ✅ IP地址哈希化存储
- ✅ 敏感参数不记录在日志中
- ✅ 请求/响应追踪使用UUID

### 响应过滤
- ✅ 健康检查移除敏感数据
- ✅ Cookie信息接口返回过滤后数据
- ✅ 错误信息不暴露内部路径

## 📊 安全监控

### 自动监控
- 异常访问模式检测
- API密钥使用统计
- IP封锁状态追踪
- 请求频率分析

### 日志类型
- 安全事件（认证失败、速率超限）
- API使用统计
- 错误和异常

## 🚨 安全建议

### 立即执行
1. **更新CORS配置** - 添加你的实际域名
2. **配置HTTPS** - 确保Zeabur启用HTTPS
3. **定期轮换密钥** - 建议每月更换API密钥

### 监控建议
1. 定期检查API使用日志
2. 监控异常的访问模式
3. 关注被封锁的IP地址

### 高级安全（可选）
1. **多级API密钥**: 为不同客户分配不同权限
2. **地理限制**: 仅允许特定地区访问
3. **时间窗口**: 设置API可用时间段

## 🔧 故障排除

### 常见错误
- `401 Unauthorized`: 检查API密钥是否正确
- `429 Too Many Requests`: 等待速率限制重置
- `403 Forbidden`: 检查CORS配置

### 应急处理
如果需要临时解除限制，可以联系管理员或重启服务。

## 📞 联系方式

如有安全问题或需要调整配置，请联系系统管理员。

---

**⚠️ 重要提醒**: 
- 请妥善保管API密钥，不要在客户端代码中暴露
- 定期检查访问日志，发现异常及时处理
- 建议使用HTTPS协议访问所有API接口 