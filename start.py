#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Zeabur部署启动脚本
"""

import os
import sys
from pathlib import Path

# 确保工作目录正确
app_dir = Path(__file__).parent
os.chdir(app_dir)

# 添加当前目录到Python路径
sys.path.insert(0, str(app_dir))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

# 设置默认环境变量（如果未设置）
os.environ.setdefault('APP_ENV', 'production')
os.environ.setdefault('DEBUG', 'false')
os.environ.setdefault('API_HOST', '0.0.0.0')
os.environ.setdefault('API_PORT', '8080')
os.environ.setdefault('LOG_LEVEL', 'INFO')

if __name__ == "__main__":
    import uvicorn
    
    print("🚀 Starting 星图超级接口线上化...")
    print(f"Python version: {sys.version}")
    print(f"Working directory: {os.getcwd()}")
    print(f"Environment: {os.environ.get('APP_ENV', 'development')}")
    
    try:
        # 导入应用
        from main import app
        print("✅ Application imported successfully")
        
        # 启动服务器
        # Zeabur使用8080端口，优先使用PORT环境变量
        port = int(os.environ.get('PORT', os.environ.get('API_PORT', 8080)))
        host = os.environ.get('API_HOST', '0.0.0.0')
        
        print(f"🌐 Starting server on {host}:{port}")
        
        uvicorn.run(
            app,
            host=host,
            port=port,
            log_level=os.environ.get('LOG_LEVEL', 'info').lower(),
            access_log=True
        )
        
    except Exception as e:
        print(f"❌ Failed to start application: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1) 