#!/usr/bin/env python3
"""
简单测试API - 用于排查Zeabur部署问题
"""

from fastapi import FastAPI
import os

app = FastAPI(title="Test API")

@app.get("/")
async def root():
    port = os.environ.get("PORT", "8080")
    return {
        "status": "ok", 
        "message": "Test API is working!",
        "port": port,
        "env_vars": {
            "APP_ENV": os.environ.get("APP_ENV"),
            "DEBUG": os.environ.get("DEBUG"),
            "API_HOST": os.environ.get("API_HOST"),
            "API_PORT": os.environ.get("API_PORT"),
            "PORT": os.environ.get("PORT")
        }
    }

@app.get("/health")
async def health():
    return {"status": "healthy", "service": "test"}

if __name__ == "__main__":
    import uvicorn
    # Zeabur默认使用8080端口
    port = int(os.environ.get("PORT", 8080))
    print(f"🌐 Starting simple test API on 0.0.0.0:{port}")
    uvicorn.run(app, host="0.0.0.0", port=port) 