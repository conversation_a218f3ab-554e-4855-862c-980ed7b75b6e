#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地测试脚本 - 测试达人信息获取功能（不启动服务器）
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

from core.cookie_manager import CookieManager
from core.xingtu_client import XingtuClient
from utils.logger import setup_logger
from config.settings import settings


async def test_author_info_fetch():
    """测试达人信息获取功能"""
    
    # 设置日志
    logger = setup_logger("test_author_info", "DEBUG")
    
    print("🧪 开始本地测试：达人信息获取")
    print("=" * 50)
    
    try:
        # 初始化组件
        print("📁 初始化组件...")
        cookie_manager = CookieManager()
        xingtu_client = XingtuClient(cookie_manager)
        
        # 检查cookie管理器状态
        print(f"🍪 Cookie管理器状态: {'健康' if cookie_manager.is_healthy() else '异常'}")
        cookies_info = cookie_manager.get_cookies_info()
        print(f"🍪 可用Cookie数量: {len(cookies_info.get('cookies', []))}")
        
        # 检查Xingtu客户端状态
        client_status = xingtu_client.get_health_status()
        print(f"🔗 Xingtu客户端状态: {'健康' if client_status['session_ready'] else '异常'}")
        print(f"🔗 Session准备状态: {client_status['session_ready']}")
        print(f"🔗 Cookies可用状态: {client_status['cookies_available']}")
        
        # 测试用的作者ID（可以修改为实际的作者ID）
        test_author_id = "Test123456"  # 请修改为真实的作者ID
        
        print(f"\n🎯 测试作者ID: {test_author_id}")
        
        # 获取可用的端点
        available_endpoints = xingtu_client.get_available_endpoints()
        print(f"📡 可用端点数量: {len(available_endpoints)}")
        
        # 显示前几个端点
        print("📡 可用端点列表（前5个）:")
        for i, endpoint in enumerate(available_endpoints[:5]):
            print(f"   {i+1}. {endpoint['name']} - {endpoint['description']}")
        
        # 尝试获取作者信息
        print(f"\n🔍 开始获取作者 {test_author_id} 的信息...")
        
        # 测试单个端点
        if available_endpoints:
            first_endpoint = available_endpoints[0]['name']
            print(f"🎯 测试端点: {first_endpoint}")
            
            result = await xingtu_client.get_author_info(
                author_id=test_author_id,
                endpoints=[first_endpoint]
            )
            
            if result:
                print("✅ 获取结果:")
                print(f"   - 成功端点数: {len(result.get('successful_endpoints', []))}")
                print(f"   - 失败端点数: {len(result.get('failed_endpoints', []))}")
                print(f"   - 总耗时: {result.get('total_time', 0):.2f}秒")
                
                # 显示成功的端点数据
                if result.get('successful_endpoints'):
                    for endpoint_name, data in result['successful_endpoints'].items():
                        print(f"   - {endpoint_name}: 数据获取成功")
                        if isinstance(data, dict) and 'data' in data:
                            data_keys = list(data['data'].keys()) if isinstance(data['data'], dict) else []
                            print(f"     数据字段: {data_keys[:5]}...")
                
                # 显示失败的端点
                if result.get('failed_endpoints'):
                    for endpoint_name, error in result['failed_endpoints'].items():
                        print(f"   - {endpoint_name}: 失败 - {error}")
            else:
                print("❌ 未获取到任何结果")
        
        print("\n🏁 测试完成")
        
        # 测试结果总结
        print("\n📊 测试结果总结:")
        print(f"   - Cookie管理器: {'✅ 正常' if cookie_manager.is_healthy() else '❌ 异常'}")
        print(f"   - Xingtu客户端: {'✅ 正常' if client_status['session_ready'] else '❌ 异常'}")
        print(f"   - 可用端点: {len(available_endpoints)}个")
        
        if result and result.get('successful_endpoints'):
            print(f"   - 数据获取: ✅ 成功")
        else:
            print(f"   - 数据获取: ❌ 失败或无数据")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


async def test_basic_functionality():
    """测试基础功能"""
    print("\n🔧 测试基础功能...")
    
    try:
        # 测试设置配置
        print(f"📄 配置环境: {settings.app.env}")
        print(f"📄 调试模式: {settings.app.debug}")
        print(f"📄 API密钥配置: {'是' if settings.app.api_key else '否'}")
        print(f"📄 Xingtu基础URL: {settings.xingtu.base_url}")
        
        # 测试cookie管理器基础功能
        cookie_manager = CookieManager()
        print(f"🍪 Cookie文件存在: {'是' if cookie_manager.cookie_file.exists() else '否'}")
        
        # 测试Xingtu客户端初始化
        client = XingtuClient(cookie_manager)
        print(f"🔗 客户端初始化: ✅ 成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 基础功能测试失败: {e}")
        return False


if __name__ == "__main__":
    print("🚀 星图超级接口线上化 - 本地测试")
    print("=" * 50)
    print("ℹ️  这是一个非服务端测试，不会启动任何HTTP服务器")
    print("ℹ️  主要测试达人信息获取功能的核心逻辑")
    print("=" * 50)
    
    # 运行基础功能测试
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        basic_test_passed = loop.run_until_complete(test_basic_functionality())
        
        if basic_test_passed:
            print("\n" + "=" * 50)
            # 运行达人信息获取测试
            loop.run_until_complete(test_author_info_fetch())
        else:
            print("❌ 基础功能测试失败，跳过达人信息测试")
            
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试运行失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        loop.close()
        print("\n👋 测试结束") 