#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test CookieCloud connection for 星图超级接口线上化
"""

import os
import sys
import time
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyCookieCloud import PyCookieCloud
from config.settings import settings


def test_cookiecloud_connection():
    """Test CookieCloud connection"""
    print("🔍 Testing CookieCloud Connection...")
    print("=" * 50)
    
    # Display configuration
    print(f"Server URL: {settings.cookiecloud.server_url}")
    print(f"UUID: {settings.cookiecloud.uuid}")
    print(f"Password: {'*' * len(settings.cookiecloud.password)}")
    print(f"Target domain: {settings.xingtu.domain}")
    print()
    
    try:
        # Create client
        print("1. Creating CookieCloud client...")
        client = PyCookieCloud(
            settings.cookiecloud.server_url,
            settings.cookiecloud.uuid,
            settings.cookiecloud.password
        )
        print("✅ Client created successfully")
        
        # Test connection by getting key
        print("\n2. Testing connection (getting key)...")
        key = client.get_the_key()
        if key:
            print("✅ Connection successful - key retrieved")
        else:
            print("❌ Failed to get key")
            return False
        
        # Get encrypted data
        print("\n3. Getting encrypted data...")
        encrypted_data = client.get_encrypted_data()
        if encrypted_data:
            print("✅ Encrypted data retrieved")
        else:
            print("❌ Failed to get encrypted data")
            return False
        
        # Get decrypted data
        print("\n4. Getting decrypted data...")
        decrypted_data = client.get_decrypted_data()
        if not decrypted_data:
            print("❌ Failed to get decrypted data")
            return False
        
        print("✅ Decrypted data retrieved successfully")
        
        # Analyze data
        print("\n5. Analyzing cookie data...")
        if not isinstance(decrypted_data, dict):
            print("❌ Invalid data format")
            return False
        
        total_cookies = 0
        domains = []
        xingtu_cookies = 0
        
        for domain, cookie_list in decrypted_data.items():
            if domain == 'update_time':
                continue
            if isinstance(cookie_list, list):
                total_cookies += len(cookie_list)
                domains.append((domain, len(cookie_list)))
                
                # Check for Xingtu cookies
                if settings.xingtu.domain in domain:
                    xingtu_cookies += len(cookie_list)
                    print(f"   🎯 Found Xingtu domain: {domain} ({len(cookie_list)} cookies)")
        
        print(f"\n📊 Cookie Statistics:")
        print(f"   Total domains: {len(domains)}")
        print(f"   Total cookies: {total_cookies}")
        print(f"   Xingtu cookies: {xingtu_cookies}")
        
        if 'update_time' in decrypted_data:
            print(f"   Last updated: {decrypted_data['update_time']}")
        
        # Show top domains
        if domains:
            print(f"\n🌐 Top domains:")
            sorted_domains = sorted(domains, key=lambda x: x[1], reverse=True)[:10]
            for domain, count in sorted_domains:
                print(f"   {domain}: {count} cookies")
        
        # Check if we have Xingtu cookies
        if xingtu_cookies > 0:
            print(f"\n✅ SUCCESS: Found {xingtu_cookies} Xingtu cookies!")
            return True
        else:
            print(f"\n⚠️  WARNING: No Xingtu cookies found for domain '{settings.xingtu.domain}'")
            print("   Available domains:")
            for domain, count in domains[:20]:  # Show first 20 domains
                print(f"     - {domain}")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_cookie_manager():
    """Test the CookieManager class"""
    print("\n" + "=" * 50)
    print("🔍 Testing CookieManager Class...")
    print("=" * 50)
    
    try:
        from core.cookie_manager import CookieManager
        
        print("1. Creating CookieManager...")
        manager = CookieManager()
        print("✅ CookieManager created")
        
        print("\n2. Loading cookies...")
        success = manager.load_cookies(force_refresh=True)
        if success:
            print("✅ Cookies loaded successfully")
        else:
            print("❌ Failed to load cookies")
            return False
        
        print("\n3. Getting cookie info...")
        info = manager.get_cookies_info()
        print(f"   Total domains: {info.get('total_domains', 0)}")
        print(f"   Total cookies: {info.get('total_cookies', 0)}")
        print(f"   Xingtu cookies: {info.get('xingtu_cookies', 0)}")
        print(f"   Available cookies: {info.get('available_cookies', 0)}")
        
        print("\n4. Getting Xingtu cookies...")
        xingtu_cookies = manager.get_domain_cookies(manager.xingtu_domain)
        print(f"   Found {len(xingtu_cookies)} Xingtu cookies")
        
        if xingtu_cookies:
            print("   Cookie names:")
            for cookie in xingtu_cookies[:5]:  # Show first 5
                print(f"     - {cookie.get('name', 'unknown')}")
        
        print("\n5. Getting cookie header...")
        header = manager.get_cookie_header()
        print(f"   Header length: {len(header)} characters")
        if header:
            print(f"   Sample: {header[:100]}...")
        
        print("\n6. Health check...")
        healthy = manager.is_healthy()
        print(f"   Healthy: {healthy}")
        
        return healthy
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function"""
    print("🚀 CookieCloud Connection Test")
    print("=" * 60)
    
    # Test direct connection
    connection_success = test_cookiecloud_connection()
    
    # Test CookieManager
    manager_success = test_cookie_manager()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Test Summary")
    print("=" * 60)
    print(f"CookieCloud Connection: {'✅ PASS' if connection_success else '❌ FAIL'}")
    print(f"CookieManager Class: {'✅ PASS' if manager_success else '❌ FAIL'}")
    
    if connection_success and manager_success:
        print("\n🎉 All tests passed! CookieCloud is working correctly.")
        return True
    else:
        print("\n⚠️  Some tests failed. Please check the configuration.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
