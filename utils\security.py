#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全防护工具模块
"""

import time
import hashlib
import secrets
from typing import Dict, List, Optional, Tuple
from collections import defaultdict, deque
from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse
import structlog

logger = structlog.get_logger("security")


class RateLimiter:
    """增强的速率限制器"""
    
    def __init__(self, requests_per_minute: int = 30, requests_per_hour: int = 100):
        self.requests_per_minute = requests_per_minute
        self.requests_per_hour = requests_per_hour
        self.minute_windows = defaultdict(lambda: deque())
        self.hour_windows = defaultdict(lambda: deque())
        self.blocked_ips = {}  # IP -> 解封时间
    
    def is_allowed(self, ip: str) -> Tuple[bool, str]:
        """检查IP是否被允许访问"""
        current_time = time.time()
        
        # 检查是否在黑名单中
        if ip in self.blocked_ips:
            if current_time < self.blocked_ips[ip]:
                remaining_time = int(self.blocked_ips[ip] - current_time)
                return False, f"IP被临时封锁，剩余时间: {remaining_time}秒"
            else:
                del self.blocked_ips[ip]
        
        # 清理过期记录
        minute_cutoff = current_time - 60
        hour_cutoff = current_time - 3600
        
        minute_window = self.minute_windows[ip]
        hour_window = self.hour_windows[ip]
        
        # 移除过期的请求记录
        while minute_window and minute_window[0] < minute_cutoff:
            minute_window.popleft()
        while hour_window and hour_window[0] < hour_cutoff:
            hour_window.popleft()
        
        # 检查分钟级限制
        if len(minute_window) >= self.requests_per_minute:
            # 临时封锁5分钟
            self.blocked_ips[ip] = current_time + 300
            logger.warning("IP超过分钟级限制被封锁", ip=ip, requests=len(minute_window))
            return False, "请求过于频繁，已被临时封锁5分钟"
        
        # 检查小时级限制
        if len(hour_window) >= self.requests_per_hour:
            # 临时封锁1小时
            self.blocked_ips[ip] = current_time + 3600
            logger.warning("IP超过小时级限制被封锁", ip=ip, requests=len(hour_window))
            return False, "请求过于频繁，已被临时封锁1小时"
        
        # 记录这次请求
        minute_window.append(current_time)
        hour_window.append(current_time)
        
        return True, "OK"


class SecurityHeaderMiddleware:
    """安全响应头中间件"""
    
    @staticmethod
    def add_security_headers(response):
        """添加安全响应头"""
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Content-Security-Policy"] = "default-src 'self'"
        response.headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=()"
        return response


class APIKeyManager:
    """API密钥管理器"""
    
    def __init__(self):
        self.api_usage = defaultdict(lambda: {"count": 0, "last_reset": time.time()})
        self.daily_limit = 1000  # 每个API密钥每日限制
    
    def validate_and_track(self, api_key: str, ip: str) -> Tuple[bool, str]:
        """验证API密钥并追踪使用情况"""
        if not api_key:
            return False, "缺少API密钥"
        
        # 这里可以从数据库或配置文件加载有效的API密钥
        # 为了演示，我们使用环境变量中的密钥
        from config.settings import settings
        if api_key != settings.app.api_key:
            logger.warning("无效的API密钥访问尝试", api_key=api_key[:10] + "...", ip=ip)
            return False, "无效的API密钥"
        
        # 检查日使用限制
        current_time = time.time()
        usage_data = self.api_usage[api_key]
        
        # 如果是新的一天，重置计数
        if current_time - usage_data["last_reset"] > 86400:  # 24小时
            usage_data["count"] = 0
            usage_data["last_reset"] = current_time
        
        # 检查是否超过日限制
        if usage_data["count"] >= self.daily_limit:
            return False, f"API密钥日使用量已达上限({self.daily_limit}次)"
        
        # 增加使用计数
        usage_data["count"] += 1
        
        logger.info("API密钥验证成功", 
                   daily_usage=usage_data["count"], 
                   daily_limit=self.daily_limit,
                   ip=ip)
        
        return True, "OK"


class SensitiveDataFilter:
    """敏感数据过滤器"""
    
    @staticmethod
    def filter_health_check_data(health_data: dict) -> dict:
        """过滤健康检查中的敏感信息"""
        filtered_data = health_data.copy()
        
        # 过滤cookie相关的敏感信息
        if "components" in filtered_data:
            if "cookie_manager" in filtered_data["components"]:
                cookie_details = filtered_data["components"]["cookie_manager"].get("details", {})
                if "cookies" in cookie_details:
                    # 只保留数量信息，不暴露具体cookie内容
                    cookie_count = len(cookie_details["cookies"]) if cookie_details["cookies"] else 0
                    filtered_data["components"]["cookie_manager"]["details"] = {
                        "cookie_count": cookie_count,
                        "status": "available" if cookie_count > 0 else "unavailable"
                    }
        
        return filtered_data
    
    @staticmethod
    def filter_cookie_info(cookie_info: dict) -> dict:
        """过滤cookie信息中的敏感数据"""
        return {
            "cookie_count": len(cookie_info.get("cookies", [])),
            "domains_count": len(cookie_info.get("domains", [])),
            "last_updated": cookie_info.get("last_updated"),
            "status": "healthy" if cookie_info.get("cookies") else "unhealthy"
        }


def generate_secure_api_key() -> str:
    """生成安全的API密钥"""
    return f"xingtu_{secrets.token_urlsafe(32)}"


def hash_ip(ip: str) -> str:
    """对IP地址进行哈希处理以保护隐私"""
    return hashlib.sha256(ip.encode()).hexdigest()[:16]


# 全局实例
rate_limiter = RateLimiter()
api_key_manager = APIKeyManager()
sensitive_filter = SensitiveDataFilter() 