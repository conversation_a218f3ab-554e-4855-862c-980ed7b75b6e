services:
  xingtu-api:
    build: .
    container_name: xingtu-super-api
    restart: unless-stopped
    ports:
      - "${API_PORT:-8080}:8080"
    environment:
      # CookieCloud Configuration
      - COOKIECLOUD_SERVER_URL=${COOKIECLOUD_SERVER_URL}
      - COOKIECLOUD_UUID=${COOKIECLOUD_UUID}
      - COOKIECLOUD_PASSWORD=${COOKIECLOUD_PASSWORD}
      
      # Application Configuration
      - APP_ENV=${APP_ENV:-production}
      - API_HOST=0.0.0.0
      - API_PORT=8080
      - DEBUG=${DEBUG:-false}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - API_KEY=${API_KEY:-}
      - CORS_ORIGINS=${CORS_ORIGINS:-*}
      - ENABLE_REQUEST_LOGGING=${ENABLE_REQUEST_LOGGING:-true}
      
      # Xingtu Configuration
      - XINGTU_BASE_URL=${XINGTU_BASE_URL:-https://www.xingtu.cn}
      - XINGTU_DOMAIN=${XINGTU_DOMAIN:-www.xingtu.cn}
      - REQUEST_TIMEOUT=${REQUEST_TIMEOUT:-30}
      - MAX_RETRIES=${MAX_RETRIES:-3}
      - RETRY_INTERVAL=${RETRY_INTERVAL:-2}
      - RATE_LIMIT_PER_MINUTE=${RATE_LIMIT_PER_MINUTE:-60}
      
      # Export Configuration
      - EXPORT_DIR=/app/exports
      - MAX_EXPORT_SIZE_MB=${MAX_EXPORT_SIZE_MB:-100}
      - EXCEL_FORMAT=${EXCEL_FORMAT:-xlsx}
      
      # Monitoring Configuration
      - HEALTH_CHECK_INTERVAL=${HEALTH_CHECK_INTERVAL:-60}
      - COOKIE_REFRESH_INTERVAL=${COOKIE_REFRESH_INTERVAL:-30}
      
      # Docker Configuration
      - TZ=${TZ:-Asia/Shanghai}
      - PUID=${PUID:-1000}
      - PGID=${PGID:-1000}
    
    volumes:
      # Persistent storage for logs and exports
      - ./data/logs:/app/logs
      - ./data/exports:/app/exports
      # Optional: Mount custom configuration
      - ./.env:/app/.env:ro
    
    networks:
      - xingtu-network
    
    # Security options (解决seccomp警告)
    security_opt:
      - no-new-privileges:true
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Optional: Nginx reverse proxy for production
  nginx:
    image: nginx:alpine
    container_name: xingtu-nginx
    restart: unless-stopped
    ports:
      - "${NGINX_PORT:-80}:80"
      - "${NGINX_SSL_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./data/nginx-logs:/var/log/nginx
    depends_on:
      - xingtu-api
    networks:
      - xingtu-network
    profiles:
      - production

  # Optional: Redis for caching (future enhancement)
  redis:
    image: redis:7-alpine
    container_name: xingtu-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - ./data/redis:/data
    networks:
      - xingtu-network
    profiles:
      - cache

networks:
  xingtu-network:
    driver: bridge

volumes:
  logs:
  exports:
  redis-data:
