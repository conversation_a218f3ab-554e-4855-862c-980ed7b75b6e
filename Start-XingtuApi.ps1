# Xingtu Super API - PowerShell Start Script
# Fixed configuration for proper Docker startup

param(
    [string]$ProjectName = "xingtu-project"
)

Write-Host "Starting Xingtu Super API..." -ForegroundColor Green
Write-Host ""

# Check if .env file exists
if (-not (Test-Path ".env")) {
    Write-Host "ERROR: .env file not found. Please configure environment variables first." -ForegroundColor Red
    Read-Host "Press Enter to continue..."
    exit 1
}

# Create necessary directories
if (-not (Test-Path "data\logs")) { 
    New-Item -ItemType Directory -Path "data\logs" -Force | Out-Null 
}
if (-not (Test-Path "data\exports")) { 
    New-Item -ItemType Directory -Path "data\exports" -Force | Out-Null 
}

try {
    # Build and start services
    Write-Host "Building Docker image..." -ForegroundColor Yellow
    docker-compose -p $ProjectName build xingtu-api
    
    if ($LASTEXITCODE -ne 0) {
        throw "Docker build failed"
    }

    Write-Host "Starting services..." -ForegroundColor Yellow
    docker-compose -p $ProjectName up -d xingtu-api
    
    if ($LASTEXITCODE -ne 0) {
        throw "Docker startup failed"
    }

    # Wait for service to start
    Write-Host "Waiting for service to start..." -ForegroundColor Yellow
    Start-Sleep -Seconds 15

    # Check health status
    Write-Host "Checking service health..." -ForegroundColor Yellow
    $healthResult = docker exec xingtu-super-api curl -f -s http://localhost:8080/health 2>$null
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "SUCCESS: Service started successfully!" -ForegroundColor Green
        Write-Host "API Address: http://localhost:8080" -ForegroundColor Cyan
        Write-Host "API Documentation: http://localhost:8080/docs" -ForegroundColor Cyan
        Write-Host "Health Check: http://localhost:8080/health" -ForegroundColor Cyan
        
        if ($healthResult) {
            Write-Host ""
            Write-Host "Health Check Result:" -ForegroundColor Blue
            Write-Host $healthResult -ForegroundColor Gray
        }
    } else {
        Write-Host "ERROR: Service startup failed. Check logs:" -ForegroundColor Red
        docker logs xingtu-super-api
    }

    Write-Host ""
    Write-Host "Useful Commands:" -ForegroundColor Blue
    Write-Host "  View status: docker-compose -p $ProjectName ps" -ForegroundColor Gray
    Write-Host "  View logs: docker-compose -p $ProjectName logs -f xingtu-api" -ForegroundColor Gray
    Write-Host "  Stop service: docker-compose -p $ProjectName down" -ForegroundColor Gray
    Write-Host "  Restart service: docker-compose -p $ProjectName restart xingtu-api" -ForegroundColor Gray

} catch {
    Write-Host "ERROR: $_" -ForegroundColor Red
    Write-Host "Please check if Docker is properly installed and running" -ForegroundColor Yellow
}

Write-Host ""
Read-Host "Press Enter to continue..." 