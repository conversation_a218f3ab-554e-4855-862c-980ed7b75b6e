#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Real author data test for 星图超级接口线上化
"""

import os
import sys
import time
import json
import urllib.request
import urllib.parse
import urllib.error
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def make_request(url, method="GET", data=None, timeout=60):
    """Make HTTP request using urllib"""
    try:
        if data:
            data = json.dumps(data).encode('utf-8')
        
        req = urllib.request.Request(url, data=data, method=method)
        req.add_header('Content-Type', 'application/json')
        
        start_time = time.time()
        with urllib.request.urlopen(req, timeout=timeout) as response:
            response_time = time.time() - start_time
            content = response.read().decode('utf-8')
            
            return {
                "success": True,
                "status_code": response.status,
                "content": content,
                "response_time": response_time
            }
    
    except urllib.error.HTTPError as e:
        response_time = time.time() - start_time
        content = e.read().decode('utf-8') if e.fp else ""
        
        return {
            "success": False,
            "status_code": e.code,
            "content": content,
            "response_time": response_time,
            "error": str(e)
        }
    
    except Exception as e:
        return {
            "success": False,
            "status_code": 0,
            "content": "",
            "response_time": 0,
            "error": str(e)
        }


def test_health_with_real_cookies():
    """Test health check with real CookieCloud"""
    print("🔍 Testing health check with real CookieCloud...")
    
    base_url = "http://localhost:8000"
    result = make_request(f"{base_url}/health", timeout=120)
    
    if result["success"]:
        data = json.loads(result["content"])
        status = data.get("status", "unknown")
        components = data.get("components", {})
        
        print(f"✅ Health check completed: {status}")
        print(f"   Response time: {result['response_time']:.2f}s")
        
        # Check each component
        cookie_manager = components.get("cookie_manager", {})
        xingtu_client = components.get("xingtu_client", {})
        excel_exporter = components.get("excel_exporter", {})
        
        print(f"   Cookie manager healthy: {cookie_manager.get('healthy', False)}")
        if cookie_manager.get("details"):
            details = cookie_manager["details"]
            print(f"     Total cookies: {details.get('total_cookies', 0)}")
            print(f"     Xingtu cookies: {details.get('xingtu_cookies', 0)}")
            print(f"     CSRF token available: {details.get('csrf_token_available', False)}")
        
        print(f"   Xingtu client healthy: {xingtu_client.get('healthy', False)}")
        print(f"   Excel exporter healthy: {excel_exporter.get('healthy', False)}")
        
        return status == "healthy"
    else:
        print(f"❌ Health check failed: {result.get('error', 'Unknown error')}")
        return False


def test_cookie_info():
    """Test cookie information endpoint"""
    print("\n🔍 Testing cookie information...")
    
    base_url = "http://localhost:8000"
    result = make_request(f"{base_url}/cookies/info", timeout=60)
    
    if result["success"]:
        data = json.loads(result["content"])
        print("✅ Cookie info retrieved:")
        print(f"   Total domains: {data.get('total_domains', 0)}")
        print(f"   Total cookies: {data.get('total_cookies', 0)}")
        print(f"   Xingtu cookies: {data.get('xingtu_cookies', 0)}")
        print(f"   Domains: {data.get('domains', [])}")
        print(f"   Update time: {data.get('update_time', 'Unknown')}")
        print(f"   CSRF token available: {data.get('csrf_token_available', False)}")
        
        return data.get('xingtu_cookies', 0) > 0
    else:
        print(f"❌ Cookie info failed: {result.get('error', 'Unknown error')}")
        return False


def test_author_data_fetch(author_id="7119367979820646432"):
    """Test fetching real author data"""
    print(f"\n🎯 Testing author data fetch for ID: {author_id}")
    
    base_url = "http://localhost:8000"
    
    # Test basic author info
    print("\n1. Fetching basic author information...")
    result = make_request(f"{base_url}/author/{author_id}?category=basic", timeout=120)
    
    if result["success"]:
        data = json.loads(result["content"])
        metadata = data.get("_metadata", {})
        
        print("✅ Author data retrieved successfully!")
        print(f"   Author ID: {metadata.get('author_id', 'Unknown')}")
        print(f"   Total endpoints: {metadata.get('total_endpoints', 0)}")
        print(f"   Successful requests: {metadata.get('successful_requests', 0)}")
        print(f"   Failed requests: {metadata.get('failed_requests', 0)}")
        print(f"   Success rate: {metadata.get('success_rate', 0):.2%}")
        
        # Check specific endpoint data
        if "base_info" in data and data["base_info"].get("success"):
            base_data = data["base_info"].get("data", {})
            print(f"   Author name: {base_data.get('nickname', 'Unknown')}")
            print(f"   Platform: {base_data.get('platform_name', 'Unknown')}")
            print(f"   Follower count: {base_data.get('follower_count', 'Unknown')}")
        
        return True
    else:
        print(f"❌ Author data fetch failed: {result.get('error', 'Unknown error')}")
        if result["status_code"] == 500:
            try:
                error_data = json.loads(result["content"])
                print(f"   Error details: {error_data.get('detail', 'Unknown')}")
            except:
                pass
        return False


def test_comprehensive_author_data(author_id="7119367979820646432"):
    """Test fetching comprehensive author data"""
    print(f"\n🚀 Testing comprehensive author data fetch for ID: {author_id}")
    
    base_url = "http://localhost:8000"
    
    # Test all endpoints
    print("\n1. Fetching ALL author data...")
    result = make_request(f"{base_url}/author/{author_id}", timeout=300)  # 5 minutes timeout
    
    if result["success"]:
        data = json.loads(result["content"])
        metadata = data.get("_metadata", {})
        
        print("✅ Comprehensive author data retrieved!")
        print(f"   Response time: {result['response_time']:.2f}s")
        print(f"   Author ID: {metadata.get('author_id', 'Unknown')}")
        print(f"   Total endpoints: {metadata.get('total_endpoints', 0)}")
        print(f"   Successful requests: {metadata.get('successful_requests', 0)}")
        print(f"   Failed requests: {metadata.get('failed_requests', 0)}")
        print(f"   Success rate: {metadata.get('success_rate', 0):.2%}")
        
        # Show successful endpoints
        successful_endpoints = []
        failed_endpoints = []
        
        for endpoint_name, endpoint_data in data.items():
            if endpoint_name.startswith('_'):
                continue
            
            if isinstance(endpoint_data, dict) and endpoint_data.get("success"):
                successful_endpoints.append(endpoint_name)
            else:
                failed_endpoints.append(endpoint_name)
        
        print(f"\n   Successful endpoints ({len(successful_endpoints)}):")
        for endpoint in successful_endpoints[:10]:  # Show first 10
            print(f"     ✅ {endpoint}")
        if len(successful_endpoints) > 10:
            print(f"     ... and {len(successful_endpoints) - 10} more")
        
        if failed_endpoints:
            print(f"\n   Failed endpoints ({len(failed_endpoints)}):")
            for endpoint in failed_endpoints[:5]:  # Show first 5
                print(f"     ❌ {endpoint}")
            if len(failed_endpoints) > 5:
                print(f"     ... and {len(failed_endpoints) - 5} more")
        
        return len(successful_endpoints) > 0
    else:
        print(f"❌ Comprehensive data fetch failed: {result.get('error', 'Unknown error')}")
        return False


def test_export_functionality(author_id="7119367979820646432"):
    """Test export functionality"""
    print(f"\n📊 Testing export functionality for ID: {author_id}")
    
    base_url = "http://localhost:8000"
    
    # Test JSON export
    print("\n1. Testing JSON export...")
    export_data = {
        "author_id": author_id,
        "format": "json",
        "include_raw_data": True,
        "include_summary": True
    }
    
    result = make_request(f"{base_url}/export", method="POST", data=export_data, timeout=180)
    
    if result["success"]:
        data = json.loads(result["content"])
        metadata = data.get("_metadata", {})
        
        print("✅ JSON export successful!")
        print(f"   Response time: {result['response_time']:.2f}s")
        print(f"   Data size: {len(result['content'])} bytes")
        print(f"   Success rate: {metadata.get('success_rate', 0):.2%}")
        
        return True
    else:
        print(f"❌ JSON export failed: {result.get('error', 'Unknown error')}")
        return False


def main():
    """Main test function"""
    print("🚀 Real Author Data Test for 星图超级接口线上化")
    print("=" * 60)
    print("Target Author ID: 7119367979820646432")
    print("Using real CookieCloud configuration")
    print("=" * 60)
    
    # Wait for application to start
    print("\n⏳ Waiting for application to start...")
    time.sleep(5)
    
    tests = [
        ("Health Check", test_health_with_real_cookies),
        ("Cookie Information", test_cookie_info),
        ("Basic Author Data", lambda: test_author_data_fetch("7119367979820646432")),
        ("Comprehensive Data", lambda: test_comprehensive_author_data("7119367979820646432")),
        ("Export Functionality", lambda: test_export_functionality("7119367979820646432"))
    ]
    
    results = []
    
    for name, test_func in tests:
        print(f"\n{'='*20} {name} {'='*20}")
        try:
            success = test_func()
            results.append((name, success))
            if success:
                print(f"✅ {name} PASSED")
            else:
                print(f"❌ {name} FAILED")
        except Exception as e:
            print(f"❌ {name} ERROR: {e}")
            results.append((name, False))
    
    # Final summary
    print("\n" + "=" * 60)
    print("🏁 REAL DATA TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {name}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total:.1%})")
    
    if passed >= 3:  # At least health, cookies, and basic data should work
        print("\n🎉 SUCCESS! Real author data retrieval is working!")
        print(f"✅ Successfully retrieved data for author: 7119367979820646432")
        print("✅ CookieCloud integration working")
        print("✅ Xingtu API integration functional")
        print("✅ System ready for production use!")
        return True
    else:
        print("\n⚠️  Some critical tests failed.")
        print("Please check CookieCloud configuration and network connectivity.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
