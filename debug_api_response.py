#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试API响应内容 - 查看实际的API返回数据
"""

import asyncio
import sys
import json
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

from core.cookie_manager import CookieManager
from core.xingtu_client import XingtuClient
from utils.logger import setup_logger


async def debug_single_api_call(xingtu_client, author_id, endpoint_name):
    """调试单个API调用，显示详细响应"""
    print(f"\n🔍 调试端点: {endpoint_name}")
    print("-" * 40)
    
    try:
        # 手动调用API获取原始响应
        from config.api_endpoints import XINGTU_API_ENDPOINTS
        
        # 找到对应的端点配置
        endpoint_config = None
        for ep in XINGTU_API_ENDPOINTS:
            if ep.name == endpoint_name:
                endpoint_config = ep
                break
        
        if not endpoint_config:
            print(f"❌ 未找到端点配置: {endpoint_name}")
            return
        
        print(f"📋 端点信息:")
        print(f"   名称: {endpoint_config.name}")
        print(f"   路径: {endpoint_config.path}")
        print(f"   参数: {endpoint_config.specific_params}")
        print(f"   ID参数: {endpoint_config.id_param_name}")
        
        # 调用内部方法获取原始响应
        response = await xingtu_client._make_request(endpoint_config, author_id)
        
        print(f"\n📄 API响应:")
        print(f"   类型: {type(response)}")
        
        if isinstance(response, dict):
            print(f"   错误: {response.get('error', '无')}")
            print(f"   数据: {response.get('data', '无')}")
            
            # 如果有原始响应，显示它
            if 'raw_response' in response:
                print(f"\n📝 原始响应内容:")
                raw_response = response['raw_response']
                if isinstance(raw_response, dict):
                    print(json.dumps(raw_response, indent=2, ensure_ascii=False))
                else:
                    print(f"   {raw_response}")
            
            # 显示所有响应键
            print(f"\n🔑 响应键: {list(response.keys())}")
        else:
            print(f"   内容: {response}")
            
    except Exception as e:
        print(f"❌ 调试异常: {e}")
        import traceback
        traceback.print_exc()


async def debug_api_responses():
    """调试API响应"""
    
    print("🐛 星图API响应调试工具")
    print("=" * 50)
    
    # 设置日志
    logger = setup_logger("debug_api", "DEBUG")
    
    try:
        # 初始化组件
        print("📁 初始化组件...")
        cookie_manager = CookieManager()
        xingtu_client = XingtuClient(cookie_manager)
        
        # 测试用的作者ID
        test_author_id = "6651749112423120908"
        print(f"\n🎯 测试作者ID: {test_author_id}")
        
        # 选择几个基础端点进行调试
        debug_endpoints = [
            "global_info",
            "base_info", 
            "side_base_info"
        ]
        
        for endpoint_name in debug_endpoints:
            await debug_single_api_call(xingtu_client, test_author_id, endpoint_name)
            
        # 也测试一个可能会有数据的端点
        print(f"\n" + "="*50)
        print("🔄 尝试其他可能有效的作者ID...")
        
        # 一些可能的真实作者ID示例
        possible_real_ids = [
            "107955119950",  # 这通常是更长的抖音用户ID格式
            "MS4wLjABAAAA",  # 某些平台的编码格式
        ]
        
        for test_id in possible_real_ids[:1]:  # 只测试第一个
            print(f"\n🎯 尝试作者ID: {test_id}")
            await debug_single_api_call(xingtu_client, test_id, "global_info")
        
        print(f"\n📋 调试总结:")
        print(f"✅ Cookie管理器正常工作")
        print(f"✅ API请求成功发送")
        print(f"✅ 服务器正常响应（200状态码）")
        print(f"⚠️  可能需要有效的作者ID才能获取实际数据")
        
        print(f"\n💡 下一步建议:")
        print(f"1. 从星图网站获取真实的作者ID")
        print(f"2. 检查是否需要特定的用户权限")
        print(f"3. 验证API端点是否需要额外参数")
        
    except Exception as e:
        print(f"❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 星图API响应调试")
    print("ℹ️  此工具用于查看API的实际响应内容")
    print("=" * 50)
    
    # 运行调试
    try:
        asyncio.run(debug_api_responses())
    except KeyboardInterrupt:
        print("\n⚠️  调试被用户中断")
    except Exception as e:
        print(f"\n❌ 调试运行失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n👋 调试结束") 