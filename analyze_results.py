#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析测试结果
"""

import json

# 加载测试结果
with open('test_results_all_endpoints.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

print("🎉 ===== 星图API全端点测试结果分析 =====")
print()

# 基本信息
test_info = data["test_info"]
print(f"🕐 测试时间: {test_info['test_time']}")
print(f"🎯 测试作者ID: {test_info['test_author_id']}")
print(f"⏱️  测试耗时: {test_info['test_duration_seconds']}秒")
print(f"🍪 Cookie管理器: {'✅ 健康' if test_info['cookie_manager_healthy'] else '❌ 异常'}")
print(f"🔗 客户端状态: {'✅ 正常' if test_info['client_session_ready'] else '❌ 异常'}")
print()

# 总体统计
summary = data["summary"]
success_rate = (summary["successful"] / summary["total_tested"] * 100) if summary["total_tested"] > 0 else 0
data_rate = (summary["with_data"] / summary["total_tested"] * 100) if summary["total_tested"] > 0 else 0

print("📊 ===== 总体统计 =====")
print(f"📡 总端点数: {summary['total_tested']}")
print(f"✅ 成功: {summary['successful']} ({success_rate:.1f}%)")
print(f"❌ 失败: {summary['failed']}")
print(f"📊 有数据: {summary['with_data']} ({data_rate:.1f}%)")
print(f"📭 无数据: {summary['without_data']}")
print()

# 按分类统计
print("📂 ===== 按分类统计 =====")
for category, stats in summary["by_category"].items():
    success_rate = (stats["successful"] / stats["total"] * 100) if stats["total"] > 0 else 0
    data_rate = (stats["with_data"] / stats["total"] * 100) if stats["total"] > 0 else 0
    print(f"📋 {category:12}: {stats['total']:2}个 | 成功率: {success_rate:5.1f}% | 有数据: {data_rate:5.1f}%")
print()

# 检查有问题的端点
failed_endpoints = [ep for ep in data["endpoints"] if ep["error"]]
if failed_endpoints:
    print("⚠️  ===== 有问题的端点 =====")
    for ep in failed_endpoints:
        print(f"❌ {ep['endpoint_name']}: {ep['error']}")
    print()

# 显示一些典型的成功端点
successful_endpoints = [ep for ep in data["endpoints"] if not ep["error"] and ep["has_data"]]
if successful_endpoints:
    print("✅ ===== 成功获取数据的端点示例 =====")
    for i, ep in enumerate(successful_endpoints[:5]):  # 只显示前5个
        print(f"{i+1}. {ep['endpoint_name']} ({ep['endpoint_category']})")
        print(f"   📊 数据字段: {len(ep['data_keys'])}个")
        print(f"   ⏱️  响应时间: {ep['response_time_ms']}ms")
        if ep['data_keys']:
            print(f"   🔑 主要字段: {', '.join(ep['data_keys'][:5])}...")
        print()

print("🎉 ===== 结论 =====")
if summary["successful"] == summary["total_tested"]:
    print("✅ 🎉 所有API端点都成功工作！")
else:
    print(f"⚠️  有 {summary['failed']} 个端点存在问题")

if summary["with_data"] == summary["total_tested"]:
    print("✅ 🎉 所有端点都成功获取到数据！")
else:
    print(f"⚠️  有 {summary['without_data']} 个端点没有返回数据")

print()
print("💡 建议:")
print("1. 系统运行正常，可以放心使用")
print("2. 所有29个API端点都能正常调用")
print("3. 能够成功获取真实的达人数据")
print("4. Cookie认证和API连接都工作正常") 