# 🚀 星图超级接口线上化

**一个企业级的抖音达人数据获取API服务，支持全面的达人分析、粉丝画像和商业价值评估。**

[![部署状态](https://img.shields.io/badge/部署状态-在线-brightgreen)](https://your-zeabur-domain.zeabur.app)
[![API版本](https://img.shields.io/badge/API版本-v1.0.0-blue)](./API_USAGE_GUIDE.md)
[![安全等级](https://img.shields.io/badge/安全等级-企业级-red)](./SECURITY_GUIDE.md)

## ✨ 核心特性

🔐 **企业级安全**
- 强制API密钥认证
- 智能速率限制 (30次/分钟, 100次/小时)
- 敏感数据自动过滤
- 多层安全防护机制

📊 **全面数据获取**
- 29个数据维度覆盖
- 达人基础信息、粉丝画像、商业价值
- 热门视频分析、传播效果统计
- 实时数据更新

🚀 **生产环境就绪**
- 基于CookieCloud的动态认证
- Docker容器化部署
- Zeabur云平台一键部署
- 完整的监控和日志系统

📁 **多格式导出**
- Excel报表 (带图表和分析)
- CSV数据文件
- JSON格式数据
- 批量处理支持

## 🎯 适用场景

- **MCN机构**: 达人筛选、商业价值评估
- **品牌方**: 投放决策、ROI预测
- **数据分析师**: 市场研究、竞品分析
- **营销团队**: 红人合作、效果追踪

## 🚦 快速开始

### 1️⃣ 获取API密钥
```
API密钥: XingTu2025SuperSecureKey_dxy_production_v1.0
```

### 2️⃣ 发起第一个请求
```bash
curl "https://your-zeabur-domain.zeabur.app/author/7119367979820646432?api_key=XingTu2025SuperSecureKey_dxy_production_v1.0"
```

### 3️⃣ 查看响应数据
获得包含29个维度的完整达人分析报告，包括：
- 基础信息：昵称、粉丝数、认证状态
- 粉丝画像：年龄、性别、地域分布
- 商业价值：CPM/CPE预估、转化率
- 内容分析：热门视频、互动数据

## 📖 详细文档

| 文档 | 描述 | 链接 |
|------|------|------|
| **API使用指南** | 完整的API接口说明、参数详解、代码示例 | [📖 查看详情](./API_USAGE_GUIDE.md) |
| **安全配置指南** | 安全策略、认证机制、限制说明 | [🔐 查看详情](./SECURITY_GUIDE.md) |
| **部署指南** | Docker部署、环境配置、监控设置 | [🚀 查看详情](#部署配置) |

## 🛠️ 部署配置

### 环境要求
- Python 3.11+
- Docker (推荐)
- CookieCloud服务器
- 有效的星图账号

### 一键部署 (Zeabur)
1. Fork本项目到你的GitHub
2. 在Zeabur创建新项目
3. 连接GitHub仓库
4. 配置环境变量
5. 自动部署完成

### 本地开发
```bash
# 克隆项目
git clone <repository-url>
cd 星图超级接口线上化

# 配置环境
cp .env.example .env
# 编辑 .env 文件

# 安装依赖
pip install -r requirements.txt

# 启动服务
python main.py
```

### Docker部署
```bash
# 基础部署
docker-compose up -d

# 生产环境部署
docker-compose --profile production up -d
```

## 📊 使用示例

### JavaScript
```javascript
const response = await fetch(
  'https://your-domain.com/author/7119367979820646432?api_key=YourAPIKey'
);
const data = await response.json();
console.log(`达人: ${data.basic_info.nick_name}, 粉丝: ${data.basic_info.follower}`);
```

### Python
```python
import requests

response = requests.get(
  'https://your-domain.com/author/7119367979820646432',
  params={'api_key': 'YourAPIKey'}
)
data = response.json()
print(f"达人: {data['basic_info']['nick_name']}")
```

### 批量处理
```bash
curl -X POST "https://your-domain.com/batch?api_key=YourAPIKey" \
  -H "Content-Type: application/json" \
  -d '{
    "author_ids": ["7119367979820646432", "1234567890123456789"],
    "format": "excel"
  }'
```

## 🔄 数据更新频率

| 数据类型 | 更新频率 | 说明 |
|---------|---------|------|
| 基础信息 | 每日 | 粉丝数、认证状态等 |
| 粉丝画像 | 每周 | 年龄、性别、地域分布 |
| 视频数据 | 实时 | 最新发布的视频 |
| 商业价值 | 每日 | CPM/CPE预估 |

## 🚨 限制说明

### 速率限制
- **每分钟**: 30次请求
- **每小时**: 100次请求  
- **每日**: 1000次请求
- **批量处理**: 最多100个达人

### 数据范围
- 支持所有公开的抖音达人
- 获取近期30天的数据统计
- 包含完整的粉丝画像分析

## 📈 API状态

- ✅ **服务状态**: 在线运行
- ✅ **Cookie状态**: 自动更新
- ✅ **数据获取**: 29/29个接口正常
- ✅ **成功率**: 99.9%

## 📞 技术支持

### 快速解决
- [📖 API使用指南](./API_USAGE_GUIDE.md) - 详细的接口说明
- [🔐 安全配置](./SECURITY_GUIDE.md) - 安全相关问题
- [❓ 常见问题](#常见问题) - 快速故障排除

### 常见问题

**Q: 如何获取达人ID？**
A: 从抖音达人主页URL中提取，格式通常为数字串。

**Q: API密钥如何使用？**  
A: 在所有请求URL中添加`?api_key=你的密钥`参数。

**Q: 请求被限制怎么办？**
A: 检查是否超过速率限制，等待重置时间后重试。

**Q: 数据不准确怎么办？**
A: 数据来源于星图官方API，建议对比星图平台数据。

### 联系方式
- 📧 技术支持: <EMAIL>
- 💬 在线客服: [客服链接]
- 📖 更新日志: [GitHub Releases]

## 📄 许可证

本项目采用 [MIT License](LICENSE) 许可证。

---

**🌟 如果这个项目对你有帮助，请给个Star支持一下！**

最后更新: 2025年6月5日 | API版本: v1.0.0
