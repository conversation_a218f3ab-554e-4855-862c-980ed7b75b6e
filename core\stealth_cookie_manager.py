#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
隐匿性增强的Cookie管理器
专门用于模拟真实浏览器的Cookie行为，避免检测
"""

import time
import random
import hashlib
from typing import Dict, Optional, List, Tuple
from PyCookieCloud import PyCookieCloud
import structlog

from config.settings import settings
from utils.logger import log_cookie_operation, log_error
from utils.validators import validate_cookie_format


class StealthCookieManager:
    """
    隐匿性增强的Cookie管理器
    模拟真实浏览器的Cookie处理行为
    """
    
    def __init__(self):
        """初始化隐匿性Cookie管理器"""
        self.logger = structlog.get_logger("stealth_cookie_manager")
        self.config = settings.cookiecloud
        self.xingtu_domain = settings.xingtu.domain
        self.client = None
        self.all_cookies = None
        self.last_refresh = 0
        self.refresh_interval = settings.monitoring.cookie_refresh_interval * 60
        
        # 隐匿性相关配置
        self.cookie_rotation_enabled = True
        self.cookie_noise_level = 0.1  # 10% 的Cookie添加噪声
        self.session_consistency = True  # 保持会话一致性
        self.last_rotation = 0
        self.rotation_interval = 3600  # 1小时轮换一次关键Cookie
        
        # Cookie指纹缓存
        self._cookie_fingerprint_cache = {}
        self._session_cookies = {}
        
        self._initialize_stealth_client()
    
    def _initialize_stealth_client(self):
        """初始化隐匿性CookieCloud客户端"""
        try:
            self.client = PyCookieCloud(
                self.config.server_url,
                self.config.uuid,
                self.config.password
            )
            
            # 设置更保守的连接参数
            # 如果PyCookieCloud支持自定义请求配置
            if hasattr(self.client, 'session'):
                self.client.session.timeout = 30
                # 添加真实的User-Agent
                self.client.session.headers.update({
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36'
                })
            
            log_cookie_operation(
                self.logger,
                "initialize",
                self.xingtu_domain,
                True,
                "Stealth CookieCloud client initialized"
            )

            # 立即测试连接并加载初始Cookie
            self._load_initial_cookies()

        except Exception as e:
            log_error(self.logger, e, {"operation": "initialize_stealth_cookiecloud_client"})
            raise
    
    def _load_initial_cookies(self):
        """加载初始Cookie并设置指纹"""
        try:
            test_cookies = self.client.get_decrypted_data()
            if test_cookies:
                self.all_cookies = test_cookies
                self.last_refresh = time.time()
                
                # 生成Cookie指纹
                self._generate_cookie_fingerprint()
                
                # 设置会话基线Cookie
                self._establish_session_baseline()
                
                domain_count = len([d for d in test_cookies.keys() if d != 'update_time'])
                log_cookie_operation(
                    self.logger,
                    "initialize",
                    self.xingtu_domain,
                    True,
                    f"Initial stealth cookie load: {domain_count} domains"
                )
        except Exception as e:
            log_error(self.logger, e, {"operation": "load_initial_stealth_cookies"})
    
    def _generate_cookie_fingerprint(self):
        """生成Cookie指纹，用于一致性检查"""
        if not self.all_cookies:
            return
        
        # 基于关键Cookie生成指纹
        xingtu_cookies = self.get_domain_cookies(self.xingtu_domain)
        critical_cookies = []
        
        for cookie in xingtu_cookies:
            name = cookie.get('name', '')
            # 识别关键认证Cookie
            if any(keyword in name.lower() for keyword in ['sessionid', 'csrftoken', 'auth', 'login', 'token']):
                critical_cookies.append(f"{name}={cookie.get('value', '')[:10]}")  # 只用前10位
        
        if critical_cookies:
            fingerprint_data = "|".join(sorted(critical_cookies))
            self._cookie_fingerprint_cache['primary'] = hashlib.md5(fingerprint_data.encode()).hexdigest()[:16]
            
            self.logger.debug("Cookie fingerprint generated", 
                            critical_count=len(critical_cookies),
                            fingerprint=self._cookie_fingerprint_cache['primary'])
    
    def _establish_session_baseline(self):
        """建立会话基线Cookie集"""
        xingtu_cookies = self.get_domain_cookies(self.xingtu_domain)
        
        # 保存关键会话Cookie
        for cookie in xingtu_cookies:
            name = cookie.get('name', '')
            value = cookie.get('value', '')
            
            # 保存重要的会话Cookie
            if any(keyword in name.lower() for keyword in 
                   ['sessionid', 'csrftoken', 'auth', 'login', 'user', '_ga', '_gid']):
                self._session_cookies[name] = {
                    'value': value,
                    'domain': cookie.get('domain', self.xingtu_domain),
                    'path': cookie.get('path', '/'),
                    'secure': cookie.get('secure', True),
                    'httponly': cookie.get('httponly', True),
                    'sameSite': cookie.get('sameSite', 'Lax'),
                    'last_seen': time.time()
                }
        
        self.logger.info("Session baseline established", 
                        session_cookies=len(self._session_cookies))
    
    def load_cookies(self, force_refresh: bool = False) -> bool:
        """
        加载Cookie - 隐匿性增强版本
        """
        current_time = time.time()
        
        # 检查是否需要刷新
        if not force_refresh and self.all_cookies and (current_time - self.last_refresh) < self.refresh_interval:
            # 但是检查关键Cookie是否需要轮换
            if self._should_rotate_cookies():
                self._perform_cookie_rotation()
            
            self.logger.debug("Using cached stealth cookies", 
                            time_since_refresh=current_time - self.last_refresh)
            return True
        
        try:
            self.logger.info("Loading fresh cookies from CookieCloud...")
            new_cookies = self.client.get_decrypted_data()

            if not new_cookies:
                log_cookie_operation(
                    self.logger,
                    "load",
                    self.xingtu_domain,
                    False,
                    "No cookies returned from CookieCloud"
                )
                return False

            # 检查Cookie数据格式
            if not isinstance(new_cookies, dict):
                log_cookie_operation(
                    self.logger,
                    "load",
                    self.xingtu_domain,
                    False,
                    "Invalid cookie data format from CookieCloud"
                )
                return False

            # 执行隐匿性处理
            processed_cookies = self._process_cookies_for_stealth(new_cookies)
            
            self.all_cookies = processed_cookies
            self.last_refresh = current_time
            
            # 更新指纹和会话基线
            self._generate_cookie_fingerprint()
            self._update_session_baseline()
            
            domain_count = len([d for d in processed_cookies.keys() if d != 'update_time'])
            
            log_cookie_operation(
                self.logger,
                "load",
                self.xingtu_domain,
                True,
                f"Successfully loaded and processed {domain_count} domains with stealth enhancements"
            )
            return True

        except Exception as e:
            log_error(self.logger, e, {"operation": "load_stealth_cookies"})
            self.all_cookies = None
            return False
    
    def _process_cookies_for_stealth(self, raw_cookies: Dict) -> Dict:
        """对Cookie进行隐匿性处理"""
        processed = raw_cookies.copy()
        
        # 为每个域的Cookie添加隐匿性增强
        for domain, cookies in processed.items():
            if domain == 'update_time' or not isinstance(cookies, list):
                continue
            
            enhanced_cookies = []
            for cookie in cookies:
                enhanced_cookie = self._enhance_cookie_stealth(cookie, domain)
                enhanced_cookies.append(enhanced_cookie)
            
            # 可选：添加噪声Cookie（低概率）
            if random.random() < self.cookie_noise_level and domain == self.xingtu_domain:
                noise_cookie = self._generate_noise_cookie(domain)
                if noise_cookie:
                    enhanced_cookies.append(noise_cookie)
            
            processed[domain] = enhanced_cookies
        
        return processed
    
    def _enhance_cookie_stealth(self, cookie: Dict, domain: str) -> Dict:
        """增强单个Cookie的隐匿性"""
        enhanced = cookie.copy()
        
        # 确保关键属性正确设置
        if domain == self.xingtu_domain:
            # 为星图域名的Cookie设置更真实的属性
            enhanced.setdefault('secure', True)
            enhanced.setdefault('httponly', True)
            enhanced.setdefault('sameSite', 'Lax')
            
            # 设置合理的过期时间
            if 'expires' not in enhanced and 'max-age' not in enhanced:
                # 设置1天后过期，模拟浏览器行为
                enhanced['expires'] = int(time.time() + 86400)
        
        return enhanced
    
    def _generate_noise_cookie(self, domain: str) -> Optional[Dict]:
        """生成噪声Cookie，增加指纹复杂性"""
        # 常见的无害Cookie名称
        noise_names = ['_gat', '_hjid', '_fbp', '_fbc', 'optimizelyEndUserId']
        
        if random.random() < 0.3:  # 30%概率生成噪声Cookie
            name = random.choice(noise_names)
            value = self._generate_realistic_cookie_value(name)
            
            return {
                'name': name,
                'value': value,
                'domain': domain,
                'path': '/',
                'secure': True,
                'httponly': False,
                'sameSite': 'Lax',
                'expires': int(time.time() + 3600)  # 1小时后过期
            }
        
        return None
    
    def _generate_realistic_cookie_value(self, cookie_name: str) -> str:
        """生成真实的Cookie值"""
        if '_ga' in cookie_name:
            # Google Analytics 格式
            return f"GA1.2.{random.randint(100000000, 999999999)}.{int(time.time())}"
        elif '_gid' in cookie_name:
            # Google Analytics ID 格式
            return f"GA1.2.{random.randint(100000000, 999999999)}"
        elif 'fbp' in cookie_name:
            # Facebook Pixel 格式
            return f"fb.1.{int(time.time())}.{random.randint(100000000, 999999999)}"
        else:
            # 通用随机值
            return hashlib.md5(f"{cookie_name}{time.time()}{random.random()}".encode()).hexdigest()[:32]
    
    def _should_rotate_cookies(self) -> bool:
        """检查是否应该轮换Cookie"""
        if not self.cookie_rotation_enabled:
            return False
        
        current_time = time.time()
        return (current_time - self.last_rotation) > self.rotation_interval
    
    def _perform_cookie_rotation(self):
        """执行Cookie轮换"""
        if not self.all_cookies:
            return
        
        # 轮换非关键Cookie的某些值
        xingtu_cookies = self.get_domain_cookies(self.xingtu_domain)
        rotated_count = 0
        
        for cookie in xingtu_cookies:
            name = cookie.get('name', '').lower()
            
            # 只轮换非关键Cookie
            if any(keyword in name for keyword in ['_ga', '_gid', '_hjid', 'optimizely']):
                new_value = self._generate_realistic_cookie_value(name)
                cookie['value'] = new_value
                rotated_count += 1
        
        self.last_rotation = time.time()
        
        if rotated_count > 0:
            self.logger.debug("Performed cookie rotation", 
                            rotated_count=rotated_count)
    
    def _update_session_baseline(self):
        """更新会话基线"""
        current_time = time.time()
        
        # 更新现有会话Cookie的最后见到时间
        for name, info in self._session_cookies.items():
            info['last_seen'] = current_time
    
    def get_domain_cookies(self, domain: str) -> List[Dict]:
        """获取特定域的Cookie - 隐匿性增强版本"""
        if not self.all_cookies:
            if not self.load_cookies():
                return []
        
        domain_cookies = []
        
        # 精确域名匹配
        if domain in self.all_cookies:
            domain_cookies.extend(self.all_cookies[domain])
        
        # 子域名匹配
        for cookie_domain, cookies in self.all_cookies.items():
            if cookie_domain == 'update_time':
                continue
                
            if (cookie_domain.startswith('.') and domain.endswith(cookie_domain[1:])) or \
               (domain == cookie_domain) or \
               (cookie_domain.startswith('.') and cookie_domain[1:] in domain):
                if isinstance(cookies, list):
                    domain_cookies.extend(cookies)
        
        # 应用会话一致性过滤
        if self.session_consistency:
            domain_cookies = self._filter_for_session_consistency(domain_cookies)
        
        self.logger.debug("Retrieved stealth domain cookies", 
                        domain=domain, 
                        cookie_count=len(domain_cookies))
        return domain_cookies
    
    def _filter_for_session_consistency(self, cookies: List[Dict]) -> List[Dict]:
        """过滤Cookie以保持会话一致性"""
        filtered_cookies = []
        seen_names = set()
        
        # 优先使用会话基线中的Cookie
        for cookie in cookies:
            name = cookie.get('name', '')
            
            if name in seen_names:
                continue
            
            # 如果是会话Cookie，使用基线版本
            if name in self._session_cookies:
                baseline_cookie = cookie.copy()
                baseline_info = self._session_cookies[name]
                
                # 保持某些属性的一致性
                baseline_cookie['value'] = baseline_info['value']
                baseline_cookie['domain'] = baseline_info['domain']
                
                filtered_cookies.append(baseline_cookie)
            else:
                filtered_cookies.append(cookie)
            
            seen_names.add(name)
        
        return filtered_cookies
    
    def get_xingtu_cookies(self) -> Dict[str, str]:
        """获取星图Cookie - 隐匿性增强版本"""
        cookies = self.get_domain_cookies(self.xingtu_domain)
        
        cookie_dict = {}
        for cookie in cookies:
            name = cookie.get('name')
            value = cookie.get('value')
            if name and value:
                cookie_dict[name] = value
        
        # 验证关键Cookie存在
        critical_cookies = ['sessionid', 'csrftoken']
        missing_critical = [name for name in critical_cookies 
                           if not any(name in cookie_name.lower() for cookie_name in cookie_dict.keys())]
        
        if missing_critical:
            self.logger.warning("Missing critical cookies", 
                              missing=missing_critical,
                              available_count=len(cookie_dict))
        
        return cookie_dict
    
    def get_csrf_token(self) -> Optional[str]:
        """获取CSRF令牌 - 优先从最新Cookie中获取"""
        cookies = self.get_domain_cookies(self.xingtu_domain)
        
        # 查找CSRF相关Cookie
        csrf_patterns = ['csrftoken', 'csrf_token', 'xsrf_token', '_token']
        
        for cookie in cookies:
            name = cookie.get('name', '').lower()
            for pattern in csrf_patterns:
                if pattern in name:
                    return cookie.get('value')
        
        return None
    
    def get_cookie_header(self) -> str:
        """获取Cookie头字符串 - 按真实浏览器顺序排列"""
        cookies = self.get_xingtu_cookies()
        
        if not cookies:
            return ""
        
        # 按重要性排序Cookie（模拟浏览器行为）
        priority_order = ['sessionid', 'csrftoken', '_ga', '_gid', 'lang', 'timezone']
        sorted_cookies = []
        
        # 首先添加优先级Cookie
        for priority_name in priority_order:
            for name, value in cookies.items():
                if priority_name.lower() in name.lower():
                    sorted_cookies.append(f"{name}={value}")
                    break
        
        # 然后添加其余Cookie
        for name, value in cookies.items():
            cookie_str = f"{name}={value}"
            if cookie_str not in sorted_cookies:
                sorted_cookies.append(cookie_str)
        
        return "; ".join(sorted_cookies)
    
    def refresh_cookies(self) -> bool:
        """强制刷新Cookie"""
        old_fingerprint = self._cookie_fingerprint_cache.get('primary')
        
        success = self.load_cookies(force_refresh=True)
        
        if success:
            new_fingerprint = self._cookie_fingerprint_cache.get('primary')
            if old_fingerprint != new_fingerprint:
                self.logger.info("Cookie fingerprint changed after refresh",
                               old=old_fingerprint, new=new_fingerprint)
        
        return success
    
    def is_healthy(self) -> bool:
        """检查Cookie管理器健康状态"""
        if not self.all_cookies:
            return False
        
        # 检查关键Cookie是否存在
        xingtu_cookies = self.get_xingtu_cookies()
        
        # 至少需要一些基本Cookie
        if len(xingtu_cookies) < 3:
            return False
        
        # 检查会话一致性
        fingerprint = self._cookie_fingerprint_cache.get('primary')
        if not fingerprint:
            return False
        
        return True
    
    def get_cookies_info(self) -> Dict:
        """获取Cookie信息"""
        if not self.all_cookies:
            return {"cookies": {}, "domains": [], "last_updated": None, "total_count": 0}
        
        domain_count = {}
        total_cookies = 0
        
        for domain, cookies in self.all_cookies.items():
            if domain == 'update_time':
                continue
            if isinstance(cookies, list):
                domain_count[domain] = len(cookies)
                total_cookies += len(cookies)
        
        return {
            "cookies": domain_count,
            "domains": list(domain_count.keys()),
            "last_updated": self.last_refresh,
            "total_count": total_cookies,
            "stealth_features": {
                "fingerprint_active": bool(self._cookie_fingerprint_cache),
                "session_baseline": len(self._session_cookies),
                "rotation_enabled": self.cookie_rotation_enabled,
                "last_rotation": self.last_rotation
            }
        }